# Daily Report <DD-MMM-YYYY>

From last updates in the last 24 hours

## [<Epic KEY>](<Epic URL>) | <Epic Status> | <Epic Summary>

### <Task Type> | [<Task KEY>](<Task URL>) | <Task Status> | <Task Summary>

1. <HH:MM> → <Author's name> commented: <comment-content>
2. <HH:MM> → <Author's name> commented: <comment-content>
3. <HH:MM> → <Author's name> log work <worklog-time>: <worklog-content>
4. Sub-Task | [<Sub-Task KEY>](<Sub-Task URL>) | <Task Status> | <Task Summary>
   1. <HH:MM> → <Author's name> commented: <comment-content>
   2. <HH:MM> → <Author's name> commented: <comment-content>
   3. <HH:MM> → <Author's name> log work <worklog-time>: <worklog-content>
5. Sub-Task | [<Sub-Task KEY>](<Sub-Task URL>) | <Task Status> | <Task Summary>
   1. <HH:MM> → <Author's name> commented: <comment-content>
   2. <HH:MM> → <Author's name> commented: <comment-content>

## Anything else

### [<Task Type> | <Task KEY> <Task Status>: <Task Summary>](<Task URL>)

1. <HH:MM> → <Author's name> commented: <comment-content>
2. <HH:MM> → <Author's name> commented: <comment-content>
3. <HH:MM> → <Author's name> log work <worklog-time>: <worklog-content>