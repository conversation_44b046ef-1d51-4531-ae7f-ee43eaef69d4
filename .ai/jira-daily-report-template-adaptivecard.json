{"type": "AdaptiveCard", "$schema": "http://adaptivecards.io/schemas/adaptive-card.json", "version": "1.5", "msteams": {"width": "Full"}, "body": [{"type": "TextBlock", "text": "Daily Report DD-MMM-YYYY", "size": "ExtraLarge", "weight": "Bolder", "wrap": true}, {"type": "TextBlock", "text": "From last updates in the last 24 hours", "size": "Medium", "wrap": true, "spacing": "Small"}, {"type": "TextBlock", "text": "[Epic KEY](Epic URL) | Epic Status | Epic Summary", "size": "Large", "weight": "Bolder", "wrap": true, "spacing": "Medium", "separator": true}, {"type": "TextBlock", "text": "1. Task Type | [Task KEY](Task URL) | Task Status | Task Summary", "size": "Medium", "weight": "Bolder", "wrap": true, "spacing": "Small"}, {"type": "TextBlock", "text": "    1. HH:MM → Author's name commented: comment-content", "wrap": true, "size": "Small", "spacing": "Small"}, {"type": "TextBlock", "text": "    2. HH:MM → Author's name log work worklog-time: worklog-content", "wrap": true, "size": "Small", "spacing": "Small"}, {"type": "TextBlock", "text": "    3. HH:MM → Author's name log work worklog-time: worklog-content", "wrap": true, "size": "Small", "spacing": "Small"}, {"type": "TextBlock", "text": "4. Sub-Task | [Sub-Task KEY](Sub-Task URL) | Sub-Task Status | Sub-Task Summary", "wrap": true, "size": "Small", "weight": "Bolder", "spacing": "Small"}, {"type": "TextBlock", "text": "       1. HH:MM → Author's name commented: comment-content", "wrap": true, "size": "Small", "spacing": "Small"}, {"type": "TextBlock", "text": "       2. HH:M<PERSON> → Author's name commented: comment-content", "wrap": true, "size": "Small", "spacing": "Small"}, {"type": "TextBlock", "text": "5. Sub-Task | [Sub-Task KEY](Sub-Task URL) | Sub-Task Status | Sub-Task Summary", "wrap": true, "size": "Small", "weight": "Bolder", "spacing": "Small"}, {"type": "TextBlock", "text": "       1. HH:MM → Author's name commented: comment-content", "wrap": true, "size": "Small", "spacing": "Small"}, {"type": "TextBlock", "text": "Anything else", "size": "Large", "weight": "Bolder", "spacing": "Medium", "separator": true, "wrap": true}, {"type": "TextBlock", "text": "1. Task Type | [Task KEY](Task URL) | Task Status | Task Summary", "size": "Medium", "weight": "Bolder", "wrap": true, "spacing": "Small"}, {"type": "Container", "spacing": "Small", "style": "emphasis", "items": [{"type": "TextBlock", "text": "    1. HH:MM → Author's name commented: comment-content", "wrap": true, "size": "Small"}, {"type": "TextBlock", "text": "    2. HH:MM → Author's name log work worklog-time: worklog-content", "wrap": true, "size": "Small"}]}]}