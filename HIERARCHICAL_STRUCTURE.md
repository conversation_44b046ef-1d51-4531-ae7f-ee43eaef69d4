# Cấu trúc phân cấp mới cho Jira Report

## Tổng quan

Đã refactor logic xử lý issues để tạo ra cấu trúc cây phân cấp rõ ràng hơn, thay thế việc sử dụng `addedToReport` map riêng biệt bằng field `AddedToReport` trong struct `IssueUpdate`.

## Cấu trúc phân cấp

```
Level 1: Epic
├── Level 2: Story/Task (con trực tiếp của Epic)
│   └── Level 3: Subtask (con của Story/Task)
└── Level 2: Story/Task (con trực tiếp của Epic)
    └── Level 3: Subtask (con của Story/Task)

Hoặc:

Level 2: Story/Task (không có Epic cha)
└── Level 3: Subtask (con của Story/Task)
```

## Logic xử lý

### 1. Hàm chính: `addIssueToHierarchy`

```go
func (g *Generator) addIssueToHierarchy(ctx context.Context, issue *IssueUpdate, epicGroups map[string]*EpicGroup, noEpicIssues *[]IssueUpdate, processedIssues map[string]*IssueUpdate, lookbackTime time.Time)
```

**Chức năng**: Thêm issue vào đúng vị trí trong cây phân cấp

**Logic**:
1. Kiểm tra nếu issue đã được thêm vào report (`AddedToReport = true`) thì skip
2. Tìm parent và epic của issue
3. Nếu không có parent → Level 1 hoặc Level 2 độc lập
4. Nếu có parent → Level 3 (subtask)

### 2. Hàm tìm parent và epic: `findParentAndEpic`

```go
func (g *Generator) findParentAndEpic(ctx context.Context, issueKey string) (parentKey, epicKey string)
```

**Logic**:
- Lấy thông tin issue từ Jira API
- Nếu không có parent → return `("", "")`
- Nếu có parent:
  - Nếu parent là Epic → return `("", parentKey)` (issue này là Level 2)
  - Nếu parent là Story/Task → return `(parentKey, epicKey)` (issue này là Level 3)
    - Kiểm tra xem parent có Epic cha không để lấy `epicKey`

### 3. Hàm thêm top-level issue: `addTopLevelIssue`

```go
func (g *Generator) addTopLevelIssue(issue *IssueUpdate, epicKey string, epicGroups map[string]*EpicGroup, noEpicIssues *[]IssueUpdate)
```

**Logic**:
- Nếu có `epicKey` → thêm vào epic group tương ứng
- Nếu không có `epicKey` → thêm vào `noEpicIssues`
- Đánh dấu `AddedToReport = true`

### 4. Hàm thêm subtask: `addSubtaskToParent`

```go
func (g *Generator) addSubtaskToParent(ctx context.Context, subtask *IssueUpdate, parentKey, epicKey string, epicGroups map[string]*EpicGroup, noEpicIssues *[]IssueUpdate, processedIssues map[string]*IssueUpdate, lookbackTime time.Time)
```

**Logic**:
- Kiểm tra parent đã được xử lý chưa trong `processedIssues`
- Nếu có → thêm subtask vào parent, đảm bảo parent được thêm vào report
- Nếu chưa → fetch parent từ API, xử lý và thêm vào report
- Đánh dấu subtask `AddedToReport = true`

### 5. Hàm đảm bảo Epic group tồn tại: `ensureEpicGroupExists`

```go
func (g *Generator) ensureEpicGroupExists(epicKey string, epicGroups map[string]*EpicGroup)
```

**Logic**:
- Kiểm tra epic group đã tồn tại chưa
- Nếu chưa → fetch thông tin Epic từ API và tạo group mới

## Thay đổi trong struct

### IssueUpdate struct

```go
type IssueUpdate struct {
    Key           string
    Summary       string
    Status        string
    IssueType     string
    URL           string
    Updates       []Update
    LastUpdated   time.Time
    SubTasks      []IssueUpdate
    AddedToReport bool          // Field mới để track trạng thái
}
```

**Lợi ích**:
- Không cần map `addedToReport` riêng biệt
- Tái sử dụng `processedIssues` map hiện có
- Logic rõ ràng hơn, dễ debug

## Ví dụ xử lý

### Scenario 1: Epic với Subtask trực tiếp
```
Epic (DM1-2)
└── Subtask (DM1-5)
```

**Xử lý**:
1. DM1-5 được xử lý → `findParentAndEpic` return `("", "DM1-2")`
2. Gọi `addTopLevelIssue` với `epicKey = "DM1-2"`
3. Tạo epic group nếu chưa có
4. Thêm DM1-5 vào epic group
5. Đánh dấu `DM1-5.AddedToReport = true`

### Scenario 2: Story với Subtask (không có Epic)
```
Story (DM1-1)
└── Subtask (DM1-3)
```

**Xử lý**:
1. DM1-3 được xử lý → `findParentAndEpic` return `("DM1-1", "")`
2. Gọi `addSubtaskToParent` với `parentKey = "DM1-1"`
3. Kiểm tra DM1-1 trong `processedIssues`
4. Nếu có → thêm DM1-3 vào `DM1-1.SubTasks`
5. Nếu chưa → fetch DM1-1, thêm DM1-3 vào SubTasks
6. Thêm DM1-1 vào `noEpicIssues`
7. Đánh dấu cả DM1-1 và DM1-3 `AddedToReport = true`

## Lợi ích của cấu trúc mới

1. **Logic rõ ràng**: Cấu trúc cây phân cấp dễ hiểu
2. **Tái sử dụng code**: Sử dụng lại `processedIssues` thay vì tạo map mới
3. **Dễ maintain**: Mỗi hàm có trách nhiệm cụ thể
4. **Không duplicate**: Mỗi issue chỉ xuất hiện 1 lần trong report
5. **Flexible**: Dễ mở rộng cho các level khác nếu cần

## Test Coverage

- `TestHierarchicalStructure`: Test cấu trúc phân cấp
- `TestAddedToReportField`: Test field `AddedToReport`
- Các test hiện có vẫn pass để đảm bảo không regression
