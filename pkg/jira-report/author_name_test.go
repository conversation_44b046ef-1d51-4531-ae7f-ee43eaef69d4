package jirareport

import (
	"testing"
	"time"

	"github.com/ducminhgd/go-atlassian/jira/v3/issue"
)

// TestAuthorNameHandling tests the handling of empty/missing author names
func TestAuthorNameHandling(t *testing.T) {
	// Create config with custom default author name
	config := &Config{
		JiraHost:          "https://test.atlassian.net",
		JiraUsername:      "<EMAIL>",
		JiraPassword:      "test-token",
		WebhookURL:        "https://test.webhook.url",
		QueryType:         QueryTypeProjectAndHours,
		JiraProject:       "TEST",
		LookbackHours:     24,
		Timezone:          "UTC",
		DefaultAuthorName: "Anonymous User", // Custom name instead of "System User"
	}

	generator, err := NewGenerator(config)
	if err != nil {
		t.Fatalf("Failed to create generator: %v", err)
	}

	now := time.Now()
	lookbackTime := now.Add(-24 * time.Hour)

	// Create test issue with empty author name
	testIssue := issue.Issue{
		Key: "TEST-1",
		Fields: issue.IssueFields{
			Summary: "Test Issue",
			Status: issue.StatusDetails{
				Name: "In Progress",
			},
			IssueType: issue.IssueType{
				Name: "Story",
			},
			Comment: issue.PagedComment{
				Comments: []issue.IssueComment{
					{
						Created: now.Add(-1 * time.Hour).Format("2006-01-02T15:04:05.000-0700"),
						Author: issue.SimpleUser{
							DisplayName: "", // Empty author name
						},
						Body: "Test comment with empty author",
					},
					{
						Created: now.Add(-30 * time.Minute).Format("2006-01-02T15:04:05.000-0700"),
						Author: issue.SimpleUser{
							DisplayName: "John Doe", // Normal author name
						},
						Body: "Test comment with normal author",
					},
				},
			},
			Worklog: issue.PagedWorklog{
				Worklogs: []issue.Worklog{
					{
						Created: now.Add(-45 * time.Minute).Format("2006-01-02T15:04:05.000-0700"),
						Author: issue.SimpleUser{
							DisplayName: "", // Empty author name
						},
						Comment:   "Test worklog with empty author",
						TimeSpent: "1h",
					},
				},
			},
		},
	}

	// Process the issue
	issueUpdate := generator.processIssue(testIssue, lookbackTime)

	// Verify that we have the expected number of updates
	if len(issueUpdate.Updates) != 3 {
		t.Errorf("Expected 3 updates, got %d", len(issueUpdate.Updates))
	}

	// Check that empty author names are replaced with default
	var anonymousCount, johnDoeCount, worklogCount, commentCount int

	for _, update := range issueUpdate.Updates {
		if update.AuthorName == "" {
			t.Errorf("Found update with empty author name")
		}

		// Count different types of updates
		if update.AuthorName == "Anonymous User" {
			anonymousCount++
		}
		if update.AuthorName == "John Doe" {
			johnDoeCount++
		}
		if update.Type == "worklog" {
			worklogCount++
		}
		if update.Type == "comment" {
			commentCount++
		}
	}

	// Verify counts
	if anonymousCount != 2 {
		t.Errorf("Expected 2 updates with 'Anonymous User', got %d", anonymousCount)
	}
	if johnDoeCount != 1 {
		t.Errorf("Expected 1 update with 'John Doe', got %d", johnDoeCount)
	}
	if worklogCount != 1 {
		t.Errorf("Expected 1 worklog update, got %d", worklogCount)
	}
	if commentCount != 2 {
		t.Errorf("Expected 2 comment updates, got %d", commentCount)
	}
}

// TestGetAuthorName tests the getAuthorName helper function
func TestGetAuthorName(t *testing.T) {
	config := &Config{
		DefaultAuthorName: "Custom Default User",
	}

	generator := &Generator{
		config: config,
	}

	// Test with normal name
	result := generator.getAuthorName("John Doe")
	if result != "John Doe" {
		t.Errorf("Expected 'John Doe', got '%s'", result)
	}

	// Test with empty name
	result = generator.getAuthorName("")
	if result != "Custom Default User" {
		t.Errorf("Expected 'Custom Default User', got '%s'", result)
	}

	// Test with whitespace-only name
	result = generator.getAuthorName("   ")
	if result != "   " {
		t.Errorf("Expected whitespace to be preserved, got '%s'", result)
	}
}

// TestDefaultAuthorNameConfig tests the default configuration
func TestDefaultAuthorNameConfig(t *testing.T) {
	config := NewConfig()

	if config.DefaultAuthorName != "System User" {
		t.Errorf("Expected default author name to be 'System User', got '%s'", config.DefaultAuthorName)
	}
}

// TestCustomAuthorNameConfig tests custom configuration
func TestCustomAuthorNameConfig(t *testing.T) {
	config := &Config{
		DefaultAuthorName: "Bot User",
	}

	if config.DefaultAuthorName != "Bot User" {
		t.Errorf("Expected custom author name to be 'Bot User', got '%s'", config.DefaultAuthorName)
	}
}
