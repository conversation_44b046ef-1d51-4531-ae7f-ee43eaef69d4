package jirareport

import (
	"testing"
	"time"
)

// TestHierarchicalStructure tests the new hierarchical tree structure
func TestHierarchicalStructure(t *testing.T) {
	// Create test data that represents the hierarchical structure
	now := time.Now()

	// Test data structure:
	// Epic (DM1-2)
	//   └── Subtask (DM1-5)
	//
	// Story (DM1-1) - no epic
	//   └── Subtask (DM1-3)

	epicGroups := make(map[string]*EpicGroup)
	var noEpicIssues []IssueUpdate

	// Create Epic group
	epicGroups["DM1-2"] = &EpicGroup{
		EpicKey:     "DM1-2",
		EpicSummary: "[Story] - Init project 1-2",
		EpicStatus:  "In Progress",
		EpicURL:     "https://test.atlassian.net/browse/DM1-2",
		Issues:      []IssueUpdate{},
	}

	// Create Level 3 issue (Subtask under Epic)
	dm1_5 := IssueUpdate{
		Key:       "DM1-5",
		Summary:   "[BE] init 1-2",
		Status:    "In Progress",
		IssueType: "Subtask",
		URL:       "https://test.atlassian.net/browse/DM1-5",
		Updates: []Update{
			{
				Time:       now.Add(-1 * time.Hour),
				AuthorName: "James",
				Type:       "comment",
				Content:    "please make it more clear",
			},
		},
		LastUpdated:   now.Add(-1 * time.Hour),
		SubTasks:      []IssueUpdate{},
		AddedToReport: false,
	}

	// Mark as added to report before adding to epic
	dm1_5.AddedToReport = true

	// Add subtask directly to epic (Level 2 -> Level 1)
	epicGroups["DM1-2"].Issues = append(epicGroups["DM1-2"].Issues, dm1_5)

	// Create Level 2 issue (Story) with Level 3 subtask
	dm1_1 := IssueUpdate{
		Key:       "DM1-1",
		Summary:   "[Story] - Init project 1-1",
		Status:    "In Progress",
		IssueType: "Story",
		URL:       "https://test.atlassian.net/browse/DM1-1",
		Updates: []Update{
			{
				Time:       now.Add(-30 * time.Minute),
				AuthorName: "James",
				Type:       "comment",
				Content:    "okay",
			},
			{
				Time:       now.Add(-30 * time.Minute),
				AuthorName: "James",
				Type:       "comment",
				Content:    "fine",
			},
		},
		LastUpdated: now.Add(-30 * time.Minute),
		SubTasks: []IssueUpdate{
			{
				Key:       "DM1-3",
				Summary:   "[BE] - BE init 1-1",
				Status:    "To Do",
				IssueType: "Subtask",
				URL:       "https://test.atlassian.net/browse/DM1-3",
				Updates: []Update{
					{
						Time:       now.Add(-30 * time.Minute),
						AuthorName: "James",
						Type:       "comment",
						Content:    "good",
					},
				},
				LastUpdated:   now.Add(-30 * time.Minute),
				SubTasks:      []IssueUpdate{},
				AddedToReport: true, // Subtask is included in parent
			},
		},
		AddedToReport: false,
	}

	// Mark as added to report before adding to slice
	dm1_1.AddedToReport = true

	// Add story to noEpicIssues (no parent epic)
	noEpicIssues = append(noEpicIssues, dm1_1)

	// Generate markdown report
	markdownReport := formatMarkdownReport(epicGroups, noEpicIssues, now, "UTC")

	// Verify structure
	if len(epicGroups) != 1 {
		t.Errorf("Expected 1 epic group, got %d", len(epicGroups))
	}

	if len(noEpicIssues) != 1 {
		t.Errorf("Expected 1 no-epic issue, got %d", len(noEpicIssues))
	}

	// Verify epic group structure
	epicGroup := epicGroups["DM1-2"]
	if len(epicGroup.Issues) != 1 {
		t.Errorf("Expected 1 issue in epic group, got %d", len(epicGroup.Issues))
	}

	// Verify story structure
	story := noEpicIssues[0]
	if len(story.SubTasks) != 1 {
		t.Errorf("Expected 1 subtask in story, got %d", len(story.SubTasks))
	}

	// Verify no duplicates in report
	if !story.AddedToReport {
		t.Error("Story should be marked as added to report")
	}

	if !story.SubTasks[0].AddedToReport {
		t.Error("Subtask should be marked as added to report")
	}

	// Print report for visual verification
	t.Logf("Generated hierarchical report:\n%s", markdownReport)
}

// TestAddedToReportField tests the AddedToReport field functionality
func TestAddedToReportField(t *testing.T) {
	issue := IssueUpdate{
		Key:           "TEST-1",
		Summary:       "Test Issue",
		Status:        "In Progress",
		IssueType:     "Story",
		AddedToReport: false,
	}

	// Initially not added to report
	if issue.AddedToReport {
		t.Error("Issue should not be marked as added to report initially")
	}

	// Mark as added to report
	issue.AddedToReport = true

	if !issue.AddedToReport {
		t.Error("Issue should be marked as added to report after setting flag")
	}
}
