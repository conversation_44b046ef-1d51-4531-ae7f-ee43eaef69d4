package jirareport

import (
	"time"

	"github.com/ducminhgd/go-atlassian/internal/msteams"
)

// EpicGroup represents a group of issues under an epic
type EpicGroup struct {
	EpicKey     string
	EpicSummary string
	EpicStatus  string
	EpicURL     string
	Issues      []IssueUpdate
}

// IssueUpdate represents an issue with its updates
type IssueUpdate struct {
	Key         string
	Summary     string
	Status      string
	IssueType   string
	URL         string
	Updates     []Update
	LastUpdated time.Time
	SubTasks    []IssueUpdate // Sub-tasks belonging to this issue
}

// Update represents a single update (comment or worklog)
type Update struct {
	Time       time.Time
	AuthorName string
	Type       string // "comment" or "worklog"
	Content    string
	TimeSpent  string // for worklogs
}

// Report contains markdown and AdaptiveCard versions of the report
type Report struct {
	Markdown     string
	AdaptiveCard msteams.AdaptiveCard
}
