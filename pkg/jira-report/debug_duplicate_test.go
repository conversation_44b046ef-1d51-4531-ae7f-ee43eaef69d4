package jirareport

import (
	"strings"
	"testing"
	"time"

	"github.com/ducminhgd/go-atlassian/jira/v3/issue"
)

// TestDebugDuplicateScenario tests the exact duplicate scenario from user's report
func TestDebugDuplicateScenario(t *testing.T) {
	now := time.Now()
	lookbackTime := now.Add(-24 * time.Hour)

	// Create mock generator
	config := &Config{
		JiraHost:      "https://test.atlassian.net",
		JiraUsername:  "<EMAIL>",
		JiraPassword:  "test-token",
		WebhookURL:    "https://test.webhook.url",
		QueryType:     QueryTypeProjectAndHours,
		JiraProject:   "DM1",
		LookbackHours: 24,
		Timezone:      "UTC",
	}

	generator, err := NewGenerator(config)
	if err != nil {
		t.Fatalf("Failed to create generator: %v", err)
	}

	// Simulate the exact scenario from user's report
	// DM1-2: Story (no parent)
	// DM1-5: Subtask (parent: DM1-2)
	// DM1-1: Story (no parent)  
	// DM1-3: Subtask (parent: DM1-1)

	issues := []issue.Issue{
		// DM1-2: Story
		{
			Key: "DM1-2",
			Fields: issue.IssueFields{
				Summary: "[Story] - Init project 1-2",
				Status: issue.StatusDetails{
					Name: "In Progress",
				},
				IssueType: issue.IssueType{
					Name: "Story",
				},
				Parent: issue.ParentIssue{
					Key: "", // No parent
				},
				Comment: issue.PagedComment{
					Comments: []issue.IssueComment{},
				},
				Worklog: issue.PagedWorklog{
					Worklogs: []issue.Worklog{},
				},
			},
		},
		// DM1-5: Subtask of DM1-2
		{
			Key: "DM1-5",
			Fields: issue.IssueFields{
				Summary: "[BE] init 1-2",
				Status: issue.StatusDetails{
					Name: "In Progress",
				},
				IssueType: issue.IssueType{
					Name: "Subtask",
				},
				Parent: issue.ParentIssue{
					Key: "DM1-2",
				},
				Comment: issue.PagedComment{
					Comments: []issue.IssueComment{
						{
							Created: now.Add(-1 * time.Hour).Format("2006-01-02T15:04:05.000-0700"),
							Author: issue.SimpleUser{
								DisplayName: "James",
							},
							Body: "please make it more clear",
						},
					},
				},
				Worklog: issue.PagedWorklog{
					Worklogs: []issue.Worklog{},
				},
			},
		},
		// DM1-1: Story
		{
			Key: "DM1-1",
			Fields: issue.IssueFields{
				Summary: "[Story] - Init project 1-1",
				Status: issue.StatusDetails{
					Name: "In Progress",
				},
				IssueType: issue.IssueType{
					Name: "Story",
				},
				Parent: issue.ParentIssue{
					Key: "", // No parent
				},
				Comment: issue.PagedComment{
					Comments: []issue.IssueComment{
						{
							Created: now.Add(-30 * time.Minute).Format("2006-01-02T15:04:05.000-0700"),
							Author: issue.SimpleUser{
								DisplayName: "James",
							},
							Body: "okay",
						},
						{
							Created: now.Add(-30 * time.Minute).Format("2006-01-02T15:04:05.000-0700"),
							Author: issue.SimpleUser{
								DisplayName: "James",
							},
							Body: "fine",
						},
					},
				},
				Worklog: issue.PagedWorklog{
					Worklogs: []issue.Worklog{},
				},
			},
		},
		// DM1-3: Subtask of DM1-1
		{
			Key: "DM1-3",
			Fields: issue.IssueFields{
				Summary: "[BE] - BE init 1-1",
				Status: issue.StatusDetails{
					Name: "To Do",
				},
				IssueType: issue.IssueType{
					Name: "Subtask",
				},
				Parent: issue.ParentIssue{
					Key: "DM1-1",
				},
				Comment: issue.PagedComment{
					Comments: []issue.IssueComment{
						{
							Created: now.Add(-30 * time.Minute).Format("2006-01-02T15:04:05.000-0700"),
							Author: issue.SimpleUser{
								DisplayName: "James",
							},
							Body: "good",
						},
					},
				},
				Worklog: issue.PagedWorklog{
					Worklogs: []issue.Worklog{},
				},
			},
		},
	}

	// Simulate the processing logic
	epicGroups := make(map[string]*EpicGroup)
	var noEpicIssues []IssueUpdate
	processedIssues := make(map[string]*IssueUpdate)

	for _, iss := range issues {
		// Skip if this issue has already been processed and added to report
		if existingIssue, exists := processedIssues[iss.Key]; exists && existingIssue.AddedToReport {
			t.Logf("Skipping already processed issue: %s", iss.Key)
			continue
		}

		issueUpdate := generator.processIssue(iss, lookbackTime)
		if len(issueUpdate.Updates) == 0 {
			t.Logf("Skipping issue with no updates: %s", iss.Key)
			continue
		}

		t.Logf("Processing issue: %s", iss.Key)

		// Store the processed issue
		processedIssues[iss.Key] = &issueUpdate

		// Build the hierarchical tree based on issue level
		generator.addIssueToHierarchy(nil, iss, &issueUpdate, epicGroups, &noEpicIssues, processedIssues, lookbackTime)
	}

	// Generate report
	markdownReport := formatMarkdownReport(epicGroups, noEpicIssues, now, "UTC")

	t.Logf("Generated report:\n%s", markdownReport)

	// Count occurrences of each issue
	dm1_1_count := strings.Count(markdownReport, "DM1-1")
	dm1_2_count := strings.Count(markdownReport, "DM1-2")
	dm1_3_count := strings.Count(markdownReport, "DM1-3")
	dm1_5_count := strings.Count(markdownReport, "DM1-5")

	t.Logf("Issue counts - DM1-1: %d, DM1-2: %d, DM1-3: %d, DM1-5: %d", dm1_1_count, dm1_2_count, dm1_3_count, dm1_5_count)

	// Each issue should appear exactly once
	if dm1_1_count != 1 {
		t.Errorf("DM1-1 should appear 1 time, got %d", dm1_1_count)
	}
	if dm1_2_count != 1 {
		t.Errorf("DM1-2 should appear 1 time, got %d", dm1_2_count)
	}
	if dm1_3_count != 1 {
		t.Errorf("DM1-3 should appear 1 time, got %d", dm1_3_count)
	}
	if dm1_5_count != 1 {
		t.Errorf("DM1-5 should appear 1 time, got %d", dm1_5_count)
	}
}
