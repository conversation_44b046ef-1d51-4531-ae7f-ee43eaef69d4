# Tùy chỉnh tên tác gi<PERSON> trong Jira Report

## Vấn đề

Khi Jira API trả về comment hoặc worklog mà không có thông tin tác gi<PERSON> (author name trố<PERSON>), report sẽ hiển thị "Unknown user" hoặc để trống, g<PERSON><PERSON> khó hiểu cho người đọc.

## Giải pháp

Đã thêm tính năng tùy chỉnh tên tác giả mặc định khi thông tin author bị thiếu hoặc trống.

## Cách sử dụng

### 1. C<PERSON>u hình mặc định

```go
config := jirareport.NewConfig()
// config.DefaultAuthorName sẽ là "System User"
```

### 2. Tùy chỉnh tên tác giả

```go
config := &jirareport.Config{
    JiraHost:          "https://your-jira.atlassian.net",
    JiraUsername:      "<EMAIL>",
    JiraPassword:      "your-api-token",
    WebhookURL:        "https://your-teams-webhook",
    DefaultAuthorName: "Bot User", // Tên tùy chỉnh
    // ... các config khác
}
```

### 3. Các tùy chọn tên phổ biến

```go
// Cho hệ thống tự động
config.DefaultAuthorName = "System User"

// Cho bot
config.DefaultAuthorName = "Bot User"

// Cho người dùng ẩn danh
config.DefaultAuthorName = "Anonymous User"

// Cho automation
config.DefaultAuthorName = "Automation"

// Tên công ty/team
config.DefaultAuthorName = "DevOps Team"
```

## Cách hoạt động

### Logic xử lý

1. **Khi xử lý comment:**
   ```go
   authorName := g.getAuthorName(comment.Author.DisplayName)
   ```

2. **Khi xử lý worklog:**
   ```go
   authorName := g.getAuthorName(worklog.Author.DisplayName)
   ```

3. **Hàm getAuthorName:**
   ```go
   func (g *Generator) getAuthorName(displayName string) string {
       if displayName == "" {
           return g.config.DefaultAuthorName
       }
       return displayName
   }
   ```

### Kết quả trong report

**Trước khi sửa:**
```
Story | DM1-1 | 🔄 In Progress | [Story] - Init project 1-1
15:22 →  commented: okay  // Tên trống
15:22 → James commented: fine
```

**Sau khi sửa:**
```
Story | DM1-1 | 🔄 In Progress | [Story] - Init project 1-1
15:22 → System User commented: okay  // Sử dụng tên mặc định
15:22 → James commented: fine
```

## Cấu hình trong Config struct

```go
type Config struct {
    JiraHost     string
    JiraUsername string
    JiraPassword string
    WebhookURL   string
    Timezone     string

    // Display configuration
    DefaultAuthorName string // Tên sử dụng khi author bị thiếu/trống

    // Query configuration
    QueryType QueryType
    // ... các field khác
}
```

## Ví dụ sử dụng

### Ví dụ 1: Sử dụng mặc định

```go
package main

import (
    "context"
    "log"
    
    jirareport "github.com/ducminhgd/go-atlassian/pkg/jira-report"
)

func main() {
    config := jirareport.NewConfig()
    config.JiraHost = "https://your-jira.atlassian.net"
    config.JiraUsername = "<EMAIL>"
    config.JiraPassword = "your-api-token"
    config.WebhookURL = "https://your-teams-webhook"
    config.JiraProject = "DM1"
    // DefaultAuthorName sẽ là "System User"

    generator, err := jirareport.NewGenerator(config)
    if err != nil {
        log.Fatal(err)
    }

    report, err := generator.Generate(context.Background())
    if err != nil {
        log.Fatal(err)
    }

    // Report sẽ hiển thị "System User" cho các comment/worklog không có author
}
```

### Ví dụ 2: Tùy chỉnh tên

```go
config := &jirareport.Config{
    JiraHost:          "https://your-jira.atlassian.net",
    JiraUsername:      "<EMAIL>", 
    JiraPassword:      "your-api-token",
    WebhookURL:        "https://your-teams-webhook",
    QueryType:         jirareport.QueryTypeProjectAndHours,
    JiraProject:       "DM1",
    LookbackHours:     24,
    Timezone:          "Asia/Ho_Chi_Minh",
    DefaultAuthorName: "DevOps Bot", // Tên tùy chỉnh
}
```

### Ví dụ 3: Sử dụng environment variable

```go
import "os"

config := jirareport.NewConfig()
config.JiraHost = os.Getenv("JIRA_HOST")
config.JiraUsername = os.Getenv("JIRA_USERNAME")
config.JiraPassword = os.Getenv("JIRA_PASSWORD")
config.WebhookURL = os.Getenv("WEBHOOK_URL")
config.JiraProject = os.Getenv("JIRA_PROJECT")

// Tùy chỉnh tên author từ environment variable
if defaultAuthor := os.Getenv("DEFAULT_AUTHOR_NAME"); defaultAuthor != "" {
    config.DefaultAuthorName = defaultAuthor
}
// Nếu không set thì sẽ dùng "System User"
```

## Test Coverage

Đã thêm comprehensive test suite:

- `TestAuthorNameHandling`: Test xử lý author name trống/thiếu
- `TestGetAuthorName`: Test hàm helper getAuthorName
- `TestDefaultAuthorNameConfig`: Test config mặc định
- `TestCustomAuthorNameConfig`: Test config tùy chỉnh

Chạy test:
```bash
cd pkg/jira-report
go test -v -run TestAuthorName
```

## Lợi ích

1. **Rõ ràng hơn**: Không còn tên trống trong report
2. **Tùy chỉnh được**: Có thể đặt tên phù hợp với context
3. **Backward compatible**: Không ảnh hưởng đến code hiện có
4. **Dễ cấu hình**: Chỉ cần set một field trong config
