# Jira Configuration
JIRA_HOST=https://your-domain.atlassian.net
JIRA_USERNAME=<EMAIL>
JIRA_PASSWORD=your-api-token-here

# Microsoft Teams Configuration
TEAMS_WEBHOOK_URL=https://your-org.webhook.office.com/webhookb2/your-webhook-id

# Optional: Timezone for report timestamps (default: UTC)
# Examples: America/New_York, Europe/London, Asia/Tokyo
REPORT_TIMEZONE=UTC

# Query Configuration - Choose ONE of the following options:

# Option 1: Project + Hours (Default)
# Search for issues in a specific project updated within the last N hours
JIRA_PROJECT=PROJ
JIRA_LOOKBACK_HOURS=24

# Option 2: Custom JQL (uncomment to use instead of Option 1)
# Use any custom JQL query for maximum flexibility
# JIRA_CUSTOM_JQL=project = PROJ AND assignee = currentUser() AND updated >= -24h

# Option 3: Saved Filter (uncomment to use instead of Option 1)
# Use a saved Jira filter by its ID (find ID in filter URL)
# JIRA_FILTER_ID=12345

